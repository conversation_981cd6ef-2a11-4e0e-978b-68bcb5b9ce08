<!DOCTYPE html>
<html>
<head>
    <title>Doctor Schedule Cards</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
    <style>
        .doctor-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px;
            text-align: center;
        }
        .doctor-img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        .slick-slide {
            padding: 10px;
        }
    </style>
</head>
<body>

<div class="container mt-5">
    <h2 class="text-center mb-4">Doctor Schedule</h2>
    <div class="doctor-slider">
        <?php $__currentLoopData = $doctors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $doctor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $image = $doctor['image_url'] !== 'not uploaded'
                    ? $doctor['image_url']
                    : ($doctor['gender'] === 'Male'
                        ? asset('images/male-default.png')
                        : asset('images/female-default.png'));
            ?>
            <div>
                <div class="doctor-card">
                    <img src="<?php echo e($image); ?>" class="doctor-img mb-3" alt="<?php echo e($doctor['name']); ?>">
                    <h5><?php echo e($doctor['name']); ?></h5>
                    <p><strong>Specialty:</strong> <?php echo e($doctor['specialty']); ?></p>
                    <p><strong>Schedule:</strong> <?php echo e($doctor['schedule']); ?></p>
                    <p><strong>Branch:</strong> <?php echo e($doctor['branch']); ?></p>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
<script>
    $(document).ready(function(){
        $('.doctor-slider').slick({
            slidesToShow: 3,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 3000,
            arrows: true,
            responsive: [
                { breakpoint: 992, settings: { slidesToShow: 2 } },
                { breakpoint: 768, settings: { slidesToShow: 1 } }
            ]
        });
    });
</script>

</body>
</html>



<?php /**PATH C:\wamp64\www\tmh-live\resources\views/doctors/index.blade.php ENDPATH**/ ?>