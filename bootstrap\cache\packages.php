<?php return array (
  'biscolab/laravel-recaptcha' => 
  array (
    'providers' => 
    array (
      0 => 'Biscolab\\ReCaptcha\\ReCaptchaServiceProvider',
    ),
    'aliases' => 
    array (
      'ReCaptcha' => 'Biscolab\\ReCaptcha\\Facades\\ReCaptcha',
    ),
  ),
  'brian2694/laravel-toastr' => 
  array (
    'aliases' => 
    array (
      'Toastr' => 'Brian2694\\Toastr\\Facades\\Toastr',
    ),
    'providers' => 
    array (
      0 => 'Brian2694\\Toastr\\ToastrServiceProvider',
    ),
  ),
  'cartalyst/sentinel' => 
  array (
    'providers' => 
    array (
      0 => 'Cartalyst\\Sentinel\\Laravel\\SentinelServiceProvider',
    ),
    'aliases' => 
    array (
      'Activation' => 'Cartalyst\\Sentinel\\Laravel\\Facades\\Activation',
      'Reminder' => 'Cartalyst\\Sentinel\\Laravel\\Facades\\Reminder',
      'Sentinel' => 'Cartalyst\\Sentinel\\Laravel\\Facades\\Sentinel',
    ),
  ),
  'dirape/token' => 
  array (
    'providers' => 
    array (
      0 => 'Dirape\\Token\\TokenServiceProvider',
    ),
    'aliases' => 
    array (
      'Token' => 'Dirape\\Token\\Facades\\Facade',
    ),
  ),
  'facade/ignition' => 
  array (
    'aliases' => 
    array (
      'Flare' => 'Facade\\Ignition\\Facades\\Flare',
    ),
    'providers' => 
    array (
      0 => 'Facade\\Ignition\\IgnitionServiceProvider',
    ),
  ),
  'fruitcake/laravel-cors' => 
  array (
    'providers' => 
    array (
      0 => 'Fruitcake\\Cors\\CorsServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
);