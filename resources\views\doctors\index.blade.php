<!DOCTYPE html>
<html>
<head>
    <title>Doctor Schedule</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
    <style>
        .doctor-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px;
            text-align: center;
        }
        .doctor-img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        .slick-slide {
            padding: 10px;
        }
    </style>
</head>
<body>

<div class="container mt-4">
    <h2 class="text-center mb-4">Today's Doctor Schedule</h2>

    <!-- Filter/Search Form -->
    <form method="GET" class="form-inline justify-content-center mb-4">
        <input type="text" name="search" value="{{ $filters['search'] ?? '' }}" class="form-control mr-2" placeholder="Search by name">

        <select name="department" class="form-control mr-2">
            <option value="">All Departments</option>
            <option value="GYNAE" {{ ($filters['department'] ?? '') == 'GYNAE' ? 'selected' : '' }}>GYNAE</option>
            <option value="CARDIO" {{ ($filters['department'] ?? '') == 'CARDIO' ? 'selected' : '' }}>CARDIO</option>
            <!-- Add more departments as needed -->
        </select>

        <select name="gender" class="form-control mr-2">
            <option value="">All Genders</option>
            <option value="Male" {{ ($filters['gender'] ?? '') == 'Male' ? 'selected' : '' }}>Male</option>
            <option value="Female" {{ ($filters['gender'] ?? '') == 'Female' ? 'selected' : '' }}>Female</option>
        </select>

        <button type="submit" class="btn btn-primary">Filter</button>
    </form>
    <div class="row mb-4">
        <div class="col-md-6">
            <input type="text" id="searchBox" class="form-control" placeholder="Search doctor by name...">
        </div>
    </div>
    {{-- @if(count($doctors))
        <div class="doctor-slider">
            @foreach ($doctors as $doctor)
                @php
                    $image = $doctor['image_url'] !== 'not uploaded'
                        ? $doctor['image_url']
                        : ($doctor['gender'] === 'Male'
                            ? asset('images/male-default.png')
                            : asset('images/female-default.png'));
                @endphp
                <div>
                    <div class="doctor-card">
                        <img src="{{ $image }}" class="doctor-img mb-3" alt="{{ $doctor['name'] }}">
                        <h5>{{ $doctor['name'] }}</h5>
                        <p><strong>Department:</strong> {{ $doctor['department'] }}</p>
                        <p><strong>Specialty:</strong> {{ $doctor['specialty'] }}</p>
                        <p><strong>Schedule:</strong> {{ $doctor['schedule'] }}</p>
                        <p><strong>Branch:</strong> {{ $doctor['branch'] }}</p>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <p class="text-center text-muted">No doctors scheduled for today based on your filters.</p>
    @endif --}}
    {{-- new time  --}}
    <div class="doctor-slider" id="doctorList">
        @foreach ($doctors as $doctor)
            @php
                $image = $doctor['image_url'] !== 'not uploaded'
                    ? $doctor['image_url']
                    : ($doctor['gender'] === 'Male'
                        ? asset('images/male-default.png')
                        : asset('images/female-default.png'));
            @endphp
            <div class="doctor-item">
                <div class="doctor-card">
                    <img src="{{ $image }}" class="doctor-img mb-3" alt="{{ $doctor['name'] }}">
                    <h5 class="doctor-name">{{ $doctor['name'] }}</h5>
                    <p><strong>Specialty:</strong> {{ $doctor['specialty'] }}</p>
                    <p><strong>Schedule:</strong> {{ $doctor['schedule'] }}</p>
                    <p><strong>Branch:</strong> {{ $doctor['branch'] }}</p>
                </div>
            </div>
        @endforeach
    </div>
    {{-- new time  ends--}}
</div>

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
{{-- <script>
    $(document).ready(function(){
        $('.doctor-slider').slick({
            slidesToShow: 3,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 3000,
            arrows: true,
            responsive: [
                { breakpoint: 992, settings: { slidesToShow: 2 } },
                { breakpoint: 768, settings: { slidesToShow: 1 } }
            ]
        });
    });
</script> --}}
<script>
    $(document).ready(function(){
        let slider = $('.doctor-slider');

        slider.slick({
            slidesToShow: 3,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 3000,
            arrows: true,
            responsive: [
                { breakpoint: 992, settings: { slidesToShow: 2 } },
                { breakpoint: 768, settings: { slidesToShow: 1 } }
            ]
        });

        $('#searchBox').on('keyup', function() {
            let search = $(this).val().toLowerCase();

            $('.doctor-item').each(function() {
                let name = $(this).find('.doctor-name').text().toLowerCase();
                $(this).toggle(name.includes(search));
            });

            slider.slick('refresh'); // Refresh slider layout
        });
    });
</script>


</body>
</html>
