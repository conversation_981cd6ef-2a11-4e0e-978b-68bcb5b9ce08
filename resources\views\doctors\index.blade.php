{{-- <!DOCTYPE html>
<html>
<head>
    <title>Doctors</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
<div class="container mt-5">
    <h2 class="mb-4 text-center">Doctor List</h2>
    <div class="row"> --}}
        {{-- @foreach($doctors as $doctor)
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">{{ $doctor['name'] ?? 'No Name' }}</h5>
                        <p class="card-text"><strong>Specialty:</strong> {{ $doctor['specialty'] ?? 'N/A' }}</p>
                        <p class="card-text"><strong>Department:</strong> {{ $doctor['department'] ?? 'N/A' }}</p>
                        <p class="card-text"><strong>Branch:</strong> {{ $doctor['branch'] ?? 'N/A' }}</p>
                    </div>
                </div>
            </div>
        @endforeach --}}
        {{-- @foreach ($doctors as $doctor)
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">{{ $doctor['name'] ?? 'No Name' }}</h5>
                        <p class="card-text"><strong>Specialty:</strong> {{ $doctor['specialty'] ?? 'N/A' }}</p>
                        <p class="card-text"><strong>Department:</strong> {{ $doctor['department'] ?? 'N/A' }}</p>
                        <p class="card-text"><strong>Branch:</strong> {{ $doctor['branch'] ?? 'N/A' }}</p>
                        <p class="card-text"><strong>Contact:</strong> {{ $doctor['contact'] ?? 'N/A' }}</p>
                        <p class="card-text"><strong>Schedule:</strong> {{ $doctor['schedule'] ?? 'N/A' }}</p>
                        @if(!empty($doctor['image_url']))
                            <img src="{{ $doctor['image_url'] }}" class="img-fluid mt-2" alt="Doctor Image">
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>
</body>
</html> --}}
@extends('layouts.app')

@section('content')
<div class="container">
    @if(count($doctors) > 0)
        <div class="row">
            @foreach ($doctors as $doctor)
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        @if(!empty($doctor['image_url']) && $doctor['image_url'] !== 'not uploaded')
                            <img src="{{ $doctor['image_url'] }}" class="card-img-top" alt="Doctor Image">
                        @endif
                        <div class="card-body">
                            <h5 class="card-title">{{ $doctor['name'] }}</h5>
                            <p><strong>Specialty:</strong> {{ $doctor['specialty'] }}</p>
                            <p><strong>Department:</strong> {{ $doctor['department'] }}</p>
                            <p><strong>Branch:</strong> {{ $doctor['branch'] }}</p>
                            <p><strong>Contact:</strong> {{ $doctor['contact'] }}</p>
                            <p><strong>Schedule:</strong> {{ $doctor['schedule'] }}</p>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <p>No doctors found.</p>
    @endif
</div>
@endsection

