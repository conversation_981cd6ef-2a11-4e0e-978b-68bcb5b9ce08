<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DoctorScheduleCardController extends Controller
{
    // public function index(Request $request)
    //  {
    //     $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
    //         'branch' => 'KARACHI',
    //         'department' => 'ALL',
    //         'specialty' => 'MBBS'
    //     ]);

    //     $doctors = $response->json();

    //     return view('doctors.index', compact('doctors'));
    // }
//

// public function index()
// {
//     $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
//         'branch' => 'KARACHI',
//         'department' => 'ALL',
//         'specialty' => 'MBBS',
//     ]);

//     if ($response->successful()) {
//         $body = $response->body();

//         // First decode (to convert the raw JSON string)
//         $firstDecode = json_decode($body, true);

//         // Check if $firstDecode is still a string, decode again
//         if (is_string($firstDecode)) {
//             $doctors = json_decode($firstDecode, true);
//         } else {
//             $doctors = $firstDecode;
//         }

//         // Just to be safe, fallback to empty array
//         if (!is_array($doctors)) {
//             $doctors = [];
//         }

//         return view('doctors.index', compact('doctors'));
//     } else {
//         return view('doctors.index', ['doctors' => []]);
//     }
// }
// public function index()
// {
//     $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
//         'branch' => 'KARACHI',
//         'department' => 'ALL',
//         'specialty' => 'MBBS',
//     ]);

//     if ($response->successful()) {
//         $body = $response->body();

//         $firstDecode = json_decode($body, true);

//         if (is_string($firstDecode)) {
//             $doctors = json_decode($firstDecode, true);
//         } else {
//             $doctors = $firstDecode;
//         }

//         if (!is_array($doctors)) {
//             $doctors = [];
//         }

//         dd($doctors);  // <-- Dump here to see what you got!

//         // return view('doctors.index', compact('doctors'));
//     } else {
//         dd("API request failed with status: " . $response->status());
//     }
// }

// public function index()
// {
//     $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
//         'branch' => 'KARACHI',
//         'department' => 'ALL',
//         'specialty' => 'MBBS',
//     ]);

//     if ($response->successful()) {
//         $body = $response->body();

//         // Dump raw response body, exactly as received
//         dd($body);
//     } else {
//         dd("API request failed with status: " . $response->status());
//     }
// }

public function index()
{
    $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
        'branch' => 'KARACHI',
        'department' => 'ALL',
        'specialty' => 'MBBS',
    ]);

    if ($response->successful()) {
        $body = $response->json();  // This decodes JSON automatically

        // Debug: Log the entire response structure
        Log::info('API Response Structure:', ['body' => $body]);

        // Debug: Check what keys exist in the response
        if (is_array($body)) {
            Log::info('Available keys in response:', ['keys' => array_keys($body)]);
        } else {
            Log::info('Response body is not an array:', ['type' => gettype($body), 'value' => $body]);
        }

        // Try different possible key names for the data
        $doctors = [];
        if (is_array($body)) {
            $doctors = $body['data'] ?? $body['doctors'] ?? $body['result'] ?? $body ?? [];
        }

        // Debug: Log what we extracted
        Log::info('Extracted doctors count:', ['count' => is_array($doctors) ? count($doctors) : 'not array', 'type' => gettype($doctors)]);

        return view('doctors.index', compact('doctors'));
    } else {
        Log::error('API request failed', ['status' => $response->status(), 'body' => $response->body()]);
        // If API call failed, pass empty array
        return view('doctors.index', ['doctors' => []]);
    }
}

}
