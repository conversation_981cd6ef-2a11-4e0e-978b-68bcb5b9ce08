<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DoctorScheduleCardController extends Controller
{
    public function index(Request $request)
    {
        try {
            $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
                'branch' => 'KARACHI',
                'department' => 'ALL',
                'specialty' => 'MBBS'
            ]);
            // dd($response->body());
            if (!$response->successful()) {
                throw new \Exception("API responded with status code " . $response->status());
            }

            $json = $response->json();

            if (!isset($json['status']) || $json['status'] !== 'success' || !isset($json['data'])) {
                throw new \Exception("Unexpected API structure or missing data");
            }

            $today = strtoupper(now()->format('D')); // e.g., MON, TUE

            $doctors = collect($json['data'])->filter(function ($doc) use ($today) {
                return stripos($doc['schedule'], $today) !== false;
            });

            // Filters
            if ($request->filled('department')) {
                $doctors = $doctors->where('department', $request->input('department'));
            }

            if ($request->filled('gender')) {
                $doctors = $doctors->where('gender', ucfirst(strtolower($request->input('gender'))));
            }

            if ($request->filled('search')) {
                $search = strtolower($request->input('search'));
                $doctors = $doctors->filter(fn($doc) => str_contains(strtolower($doc['name']), $search));
            }

            return view('doctor.index', [
                'doctors' => $doctors->values()->all(),
                'filters' => [
                    'department' => $request->input('department'),
                    'gender' => $request->input('gender'),
                    'search' => $request->input('search'),
                ]
            ]);

        } catch (\Throwable $e) {
            Log::error("Doctor API error: " . $e->getMessage());
            return response("Unable to fetch doctor data. Please try again later.", 500);
        }
    }
}
