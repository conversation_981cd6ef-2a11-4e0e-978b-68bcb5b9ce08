<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DoctorScheduleCardController extends Controller
{
    // public function index(Request $request)
    //  {
    //     $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
    //         'branch' => 'KARACHI',
    //         'department' => 'ALL',
    //         'specialty' => 'MBBS'
    //     ]);

    //     $doctors = $response->json();

    //     return view('doctors.index', compact('doctors'));
    // }
//

// public function index()
// {
//     $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
//         'branch' => 'KARACHI',
//         'department' => 'ALL',
//         'specialty' => 'MBBS',
//     ]);

//     if ($response->successful()) {
//         $body = $response->body();

//         // First decode (to convert the raw JSON string)
//         $firstDecode = json_decode($body, true);

//         // Check if $firstDecode is still a string, decode again
//         if (is_string($firstDecode)) {
//             $doctors = json_decode($firstDecode, true);
//         } else {
//             $doctors = $firstDecode;
//         }

//         // Just to be safe, fallback to empty array
//         if (!is_array($doctors)) {
//             $doctors = [];
//         }

//         return view('doctors.index', compact('doctors'));
//     } else {
//         return view('doctors.index', ['doctors' => []]);
//     }
// }
// public function index()
// {
//     $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
//         'branch' => 'KARACHI',
//         'department' => 'ALL',
//         'specialty' => 'MBBS',
//     ]);

//     if ($response->successful()) {
//         $body = $response->body();

//         $firstDecode = json_decode($body, true);

//         if (is_string($firstDecode)) {
//             $doctors = json_decode($firstDecode, true);
//         } else {
//             $doctors = $firstDecode;
//         }

//         if (!is_array($doctors)) {
//             $doctors = [];
//         }

//         dd($doctors);  // <-- Dump here to see what you got!

//         // return view('doctors.index', compact('doctors'));
//     } else {
//         dd("API request failed with status: " . $response->status());
//     }
// }

// public function index()
// {
//     $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
//         'branch' => 'KARACHI',
//         'department' => 'ALL',
//         'specialty' => 'MBBS',
//     ]);

//     if ($response->successful()) {
//         $body = $response->body();

//         // Dump raw response body, exactly as received
//         dd($body);
//     } else {
//         dd("API request failed with status: " . $response->status());
//     }
// }

public function index()
{
    try {
        $response = Http::get('http://110.38.68.210:8080/isr/tmh/api/doctor', [
            'branch' => 'KARACHI',
            'department' => 'ALL',
            'specialty' => 'MBBS',
        ]);

        // Log response details for debugging
        Log::info('API Response Details:', [
            'status' => $response->status(),
            'successful' => $response->successful(),
            'headers' => $response->headers(),
            'raw_body' => $response->body(),
            'body_length' => strlen($response->body())
        ]);

        if ($response->successful()) {
            $rawBody = $response->body();

            // Check if we have any content
            if (empty($rawBody)) {
                Log::warning('API returned empty response body');
                return view('doctors.index', ['doctors' => []]);
            }

            // Extract JSON from HTML <pre> tags if present
            if (strpos($rawBody, '<pre>') !== false) {
                // Extract content between <pre> and </pre> tags
                preg_match('/<pre>(.*?)<\/pre>/s', $rawBody, $matches);
                if (isset($matches[1])) {
                    $jsonString = trim($matches[1]);
                    Log::info('Extracted JSON from HTML:', ['json' => $jsonString]);
                } else {
                    Log::error('Could not extract JSON from HTML pre tags');
                    return view('doctors.index', ['doctors' => []]);
                }
            } else {
                $jsonString = $rawBody;
            }

            // Try to decode JSON
            $body = json_decode($jsonString, true);

            // Check for JSON decode errors
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON decode error:', [
                    'error' => json_last_error_msg(),
                    'json_string' => $jsonString
                ]);
                return view('doctors.index', ['doctors' => []]);
            }

            // Debug: Log the decoded response structure
            Log::info('Decoded API Response:', ['body' => $body]);

            // Try different possible key names for the data
            $doctors = [];
            if (is_array($body)) {
                Log::info('Available keys in response:', ['keys' => array_keys($body)]);
                $doctors = $body['data'] ?? $body['doctors'] ?? $body['result'] ?? $body ?? [];

                // Additional debugging for data extraction
                Log::info('Data extraction details:', [
                    'body_data_exists' => isset($body['data']),
                    'body_data_type' => isset($body['data']) ? gettype($body['data']) : 'not set',
                    'body_data_count' => isset($body['data']) && is_array($body['data']) ? count($body['data']) : 'not countable'
                ]);
            }

            // Debug: Log what we extracted
            Log::info('Extracted doctors:', [
                'count' => is_array($doctors) ? count($doctors) : 'not array',
                'type' => gettype($doctors),
                'sample' => is_array($doctors) && count($doctors) > 0 ? array_slice($doctors, 0, 1) : 'no data'
            ]);

            // Final debug before sending to view
            Log::info('Sending to view:', [
                'doctors_count' => count($doctors),
                'doctors_sample' => count($doctors) > 0 ? $doctors[0] : 'empty'
            ]);

            return view('doctors.index', compact('doctors'));
        } else {
            Log::error('API request failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'headers' => $response->headers()
            ]);
            return view('doctors.index', ['doctors' => []]);
        }
    } catch (\Exception $e) {
        Log::error('Exception in doctor API call:', [
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return view('doctors.index', ['doctors' => []]);
    }
}

}
