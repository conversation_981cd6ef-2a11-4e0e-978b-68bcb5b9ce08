<?php

use Illuminate\Support\Facades\Route;
// use App\Http\Controllers\Routes as routFunction;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\CareerController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\EducationController;
use App\Http\Controllers\GalleryController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ServicesController;
use App\Http\Controllers\ClinicalController;
use App\Http\Controllers\contactForm;
use App\Http\Controllers\EventsController;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\ForgotPassword;
use App\Http\Controllers\SigninController;
use App\Http\Controllers\SignupController;
use App\Http\Controllers\UsersController;
use App\Http\Controllers\BlogController;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\PdfController;
use App\Http\Controllers\DoctorScheduleCardController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
// For clear-cache Routes
Route::get('/clear-cache', function(){
    Artisan::call('cache:clear');
    Artisan::call('route:cache');
    Artisan::call('view:clear');
    echo "Done";
});

// For HomeController
Route::get('/',[HomeController::class,'home']);
Route::get('/index',[HomeController::class,'home']);
Route::get('/event', [HomeController::class, 'EventLoad'])->name('business_id');

// Single Routes
Route::get('about',[AboutController::class,'about']);
Route::get('education',[EducationController::class,'education']);
Route::get('logout', [UsersController::class, 'logout']);

// For EventsController
Route::get('event_page',[EventsController::class,'eventData']);
Route::get('events_detail',[EventsController::class,'events_detail']);

// For ContactController
Route::post('contact_from', [ContactController::class, 'contact_from']);
Route::get('contact',[ContactController::class,'contact']);

// For ClinicalController
Route::get('meet_doctors',[ClinicalController::class,'meet_doctors']);
Route::get('doctor_schedule',[ClinicalController::class,'doctor_schedule']);
Route::get('doctor_detail',[ClinicalController::class,'doctor_detail']);
Route::get('departments',[ClinicalController::class,'departments']);
Route::get('departments_detail/{depart_slug}',[ClinicalController::class,'department_detail']);

// For AppointmentController
Route::get('appointment',[AppointmentController::class,'appointment']);
Route::post('submitappoint',[AppointmentController::class,'submitAppoint']);
Route::post('docDetail', [AppointmentController::class, 'docDetail']);
Route::post('loadSchedule', [AppointmentController::class, 'loadSchedule']);

// For CareerController
Route::get('opportunity',[CareerController::class,'opportunity']);
Route::get('oppor_detail',[CareerController::class,'oppor_detail']);
Route::get('apply_job',[CareerController::class,'apply_job']);
Route::post('jobform', [CareerController::class, 'jobform']);

// For ServicesController
Route::get('services',[ServicesController::class,'services']);
Route::get('services_detail/{slug}',[ServicesController::class,'service_detail']);

// For signUpController
Route::get('signUp', [SignupController::class, 'signUp']);
Route::post('/check', [SignupController::class,'checkEmail']);
Route::post('registersubmit', [SignupController::class,'registersubmit']);

// For signInController
Route::get('/verify/{code}', [SigninController::class, 'verify_user']);
Route::get('verify_email', [SigninController::class, 'verify_email']);
Route::get('/signIn', [SigninController::class, 'signIn']);
Route::post('LoginSubmit', [SigninController::class, 'LoginSubmit']);

// For forgotPassword
Route::get('/forgotPass', [forgotPassword::class, 'forgotPass']);
Route::post('/resetpassword', [forgotPassword::class, 'resetpassword']);
Route::get('/resertPassLink/{email}/{code}', [ForgotPassword::class, 'resertPassLink']);
Route::post('/updatePass', [forgotPassword::class, 'updatePass'])->name('reset.password.post');

// For ProfileController
Route::get('/profile',[ProfileController::class,'profile']);
Route::post('loadAppointment', [ProfileController::class, 'loadAppointment']);
Route::get('/editProfile',[ProfileController::class, 'editProfile']);
Route::post('/updateProfile', [ProfileController::class, 'updateProfile'])->name('update.profile.post');
Route::post('/emailUpdate', [ProfileController::class, 'emailUpdate']);
Route::post('/UpdatePassword', [ProfileController::class, 'UpdatePassword'])->name('update.password');
Route::post('/UpdateNum', [ProfileController::class, 'UpdateNum'])->name('update.number');
Route::post('/oldPassCheck', [ProfileController::class, 'oldPassCheck']);
Route::post('/emailCheck', [ProfileController::class, 'emailCheck']);

// For blogController
Route::get('blog', [BlogController::class, 'blog']);
// Route::get('{slug}', [BlogController::class, 'blog_detail'])->name('blog_detail');
Route::get('blog_detail/{slug}', [BlogController::class, 'blog_detail'])->name('blog_detail');

// For Gallery/videos
Route::get('/gallery', [GalleryController::class, 'gallery_index']);
Route::get('videos', [GalleryController::class, 'video_index']);

//For Pdf
Route::get('/newsletter', [PdfController::class, 'index'])->name('pdfs.index');
Route::post('/newsletter', [PdfController::class, 'store'])->name('pdfs.store');
Route::get('/newsletter/{slug}', [PdfController::class, 'show'])->name('pdfs.show');
Route::get('/linkstorage', function () {
    Artisan::call('storage:link');
    return 'Storage linked!';
});

//FOR Doctor schedule card
Route::get('/doctorscard', [DoctorScheduleCardController::class, 'index']);

//Mobile APIS
//
Route::get('api_events',[ApiController::class,'get_events']);
Route::get('api_services',[ApiController::class,'get_services']);
Route::get('api_departments',[ApiController::class,'get_departments']);
Route::get('api_jobs',[ApiController::class,'get_jobs']);
Route::get('api_doctors',[ApiController::class,'get_doctors']);
Route::get('api_blogs',[ApiController::class,'get_blogs']);
Route::get('api_blogs_by_id/{id}',[ApiController::class,'get_blogs_by_id']);
Route::get('api_doctor_by_id/{id}',[ApiController::class,'get_doctor_by_id']);
Route::get('api_events_by_id/{id}',[ApiController::class,'get_events_by_id']);
Route::get('api_services_by_id/{id}',[ApiController::class,'get_services_by_id']);
Route::get('api_departments_by_id/{id}',[ApiController::class,'get_departments_by_id']);
Route::get('api_jobs_by_id/{id}',[ApiController::class,'get_jobs_by_id']);

// Route::get('/name_new', [HomeController::class, 'User_IP']);
// Route::get('events', 'EventsController@index');
// Route::get('events/create', 'EventsController@create');
// Route::post('events/create', 'EventsController@store');
// Route::get('events/edit/{id}', 'EventsController@edit');
// Route::post('events/edit/{id}', 'EventsController@update');
